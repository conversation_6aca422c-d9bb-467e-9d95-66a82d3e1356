using Fluxor;
using HomeFinances.Models;
using HomeFinances.Services;
using HomeFinances.Store.BeeHiveTransactions;
using HomeFinances.Store.BankTransactions;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;

namespace HomeFinances.Components.Pages;

public partial class BankTransactions : ComponentBase, IDisposable
{
    private Dictionary<string, bool> loadingAiSuggestions = new();
    private HashSet<string> rowsInEditMode = new();

    private double windowWidth;

    [Inject] private IState<BeeHiveTransactionState> BeehiveTransactionState { get; set; } = default!;

    [Inject] private IState<BankTransactionState> BankTransactionState { get; set; } = default!;

    [Inject] private IDispatcher Dispatcher { get; set; } = default!;

    [Inject] private CategoryMappingService CategoryMappingService { get; set; } = default!;

    [Inject] private ISnackbar Snackbar { get; set; } = default!;

    [Inject] private IJSRuntime JsRuntime { get; set; } = default!;

    [Inject] private OpenAiCategoryService OpenAiCategoryService { get; set; } = default!;

    [Inject] private CategoryService CategoryService { get; set; } = default!;

    [Inject] private ILogger<BankTransactions> Logger { get; set; } = default!;

    private List<BeeHiveTransaction> SortedTransactions => BeehiveTransactionState.Value.Transactions
       .OrderByDescending(keySelector: t => t.TransactionDate)
       .ToList();

    public void Dispose()
    {
        BeehiveTransactionState.StateChanged -= OnStateHasChanged;
    }

    protected override void OnInitialized()
    {
        base.OnInitialized();
        windowWidth = 800; // Default width
        BeehiveTransactionState.StateChanged += OnStateHasChanged;

        // BankTransactions component only dispatches its own events
        if (BeehiveTransactionState.Value.LoadState.Status == LoadingStatusEnum.NotLoaded)
            Dispatcher.Dispatch(action: new BeeHiveTransactionActions.LoadBeehiveTransactionsAction());

        if (BankTransactionState.Value.Loading.Status == LoadingStatusEnum.NotLoaded)
            Dispatcher.Dispatch(action: new BankTransactionActions.LoadTransactionsAction());
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JsRuntime.InvokeVoidAsync(identifier: "registerResizeListener"
                                            , DotNetObjectReference.Create(value: this));
            windowWidth = await JsRuntime.InvokeAsync<double>(identifier: "blazorResize.getInnerWidth");
            StateHasChanged();
        }
    }

    private void OnStateHasChanged(object? sender
        , EventArgs e)
    {
        _ = InvokeAsync(workItem: StateHasChanged);
    }

    private bool IsRowInEditMode(BeeHiveTransaction transaction)
    {
        return rowsInEditMode.Contains(item: transaction.Id.ToString());
    }

    private void ToggleEditMode(BeeHiveTransaction transaction)
    {
        if (IsRowInEditMode(transaction: transaction))
            rowsInEditMode.Remove(item: transaction.Id.ToString());
        else
            rowsInEditMode.Add(item: transaction.Id.ToString());
        StateHasChanged();
    }

    private async Task SaveCategoryChanges(BeeHiveTransaction transaction)
    {
        try
        {
            // Update the category mapping
            CategoryMappingService.UpdateOrAddMapping(
                description: transaction.Description
                ,
                category: transaction.Taxonomy.Category
                ,
                subCategory: transaction.Taxonomy.SubCategory
            );

            // Remove the row from edit mode
            rowsInEditMode.Remove(item: transaction.Id.ToString());

            // Update the transaction in the state
            var updatedTransaction = transaction with
            {
                Taxonomy = new Taxonomy
                {
                    Category = transaction.Taxonomy.Category
                                                        ,
                    SubCategory = transaction.Taxonomy.SubCategory
                }
            };

            // Dispatch an action to update the transaction in the state
            Dispatcher.Dispatch(action: new BeeHiveTransactionActions.LoadBeehiveTransactionsAction());

            Snackbar.Add(message: "Category changes saved successfully"
                         , severity: Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add(message: $"Error saving category changes: {ex.Message}"
                         , severity: Severity.Error);
            // Keep the row in edit mode if there was an error
            StateHasChanged();
        }
    }

    private async Task GetAiSuggestion(BeeHiveTransaction transaction)
    {
        var transactionId = transaction.Id.ToString();
        loadingAiSuggestions[key: transactionId] = true;
        StateHasChanged();

        try
        {
            var (category, subCategory) = await OpenAiCategoryService.GetCategorySuggestion(
                transactionDescription: transaction.Description
                ,
                amount: transaction.TransactionAmount
            );

            // Create updated transaction
            var updatedTransaction = transaction with
            {
                Taxonomy = new Taxonomy
                {
                    Category = category,
                    SubCategory = subCategory
                }
            };

            // Update the category mapping
            CategoryMappingService.UpdateOrAddMapping(
                description: transaction.Description
                ,
                category: category
                ,
                subCategory: subCategory
            );

            // Put the row in edit mode with the AI suggestion
            rowsInEditMode.Add(item: transactionId);

            // Update just this transaction in the state
            Dispatcher.Dispatch(action: new BeeHiveTransactionActions.UpdateTransactionAction(Transaction: updatedTransaction));
        }
        catch (Exception ex)
        {
            Snackbar.Add(message: $"Error getting AI suggestion: {ex.Message}"
                         , severity: Severity.Error);
        }
        finally
        {
            loadingAiSuggestions[key: transactionId] = false;
            StateHasChanged();
        }
    }

    private bool IsLoadingAiSuggestion(BeeHiveTransaction transaction)
    {
        return loadingAiSuggestions.TryGetValue(key: transaction.Id.ToString()
                                                , value: out var loading) && loading;
    }

    [JSInvokable]
    public async Task OnBrowserResize()
    {
        windowWidth = await JsRuntime.InvokeAsync<double>(identifier: "blazorResize.getInnerWidth");
        StateHasChanged();
    }
}